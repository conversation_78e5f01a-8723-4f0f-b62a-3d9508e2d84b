'use client'
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { fetchCoachSocialMediaLinks, handleCoachInputChange } from '@/store/slices/coach/coachProfileSlice';
import FlexibleSocialMediaEdit from '@/components/common/FlexibleSocialMediaEdit';

const TestFlexibleEditPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { coachSocialMediaList, toggleSocialMedia, loading } = useSelector((state: RootState) => state.coachProfile);

  useEffect(() => {
    dispatch(fetchCoachSocialMediaLinks());
  }, [dispatch]);

  const handleToggleSection = (checked: boolean) => {
    dispatch(handleCoachInputChange({
      name: 'toggleSocialMedia',
      value: checked
    }));
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Flexible Social Media Edit Test</h1>
      
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Features</h2>
        <ul className="list-disc list-inside space-y-2 text-sm">
          <li><strong>Individual Save:</strong> Click pencil → edit → click green checkmark to save that platform only</li>
          <li><strong>Batch Save:</strong> Edit multiple platforms → click "Save All" button to save all changes at once</li>
          <li><strong>API Payload:</strong> Sends array of social media objects as per your sample data format</li>
          <li><strong>Real-time Updates:</strong> Changes are reflected immediately after successful API calls</li>
        </ul>
      </div>

      <div className="mb-8 p-4 bg-yellow-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Expected API Payload Format</h2>
        <pre className="text-sm overflow-auto bg-white p-3 rounded">
{`[
  {
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "Instagram",
    "socialMediaLink": "https://instagram.com/coach_profile",
    "isHidden": true
  },
  {
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "X",
    "socialMediaLink": "https://X.com/in/coach_profile",
    "isHidden": true
  }
]`}
        </pre>
      </div>

      <FlexibleSocialMediaEdit
        origin="coach"
        list={coachSocialMediaList}
        toggleSocialMediaSection={toggleSocialMedia}
        onChangeToggleSection={handleToggleSection}
        loading={loading}
        fetchLoading={loading}
      />

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Current State</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(coachSocialMediaList, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestFlexibleEditPage;
