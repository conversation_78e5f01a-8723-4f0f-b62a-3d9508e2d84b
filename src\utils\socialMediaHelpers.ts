import { EachSocialMediaItem } from "./interfaces";

/**
 * Extract Instagram link from social media list
 * @param socialMediaList - Array of social media items
 * @returns Instagram link if exists and is not hidden, empty string otherwise
 */
export const getInstagramLink = (socialMediaList: EachSocialMediaItem[]): string => {
  const instagramItem = socialMediaList?.find(
    (item) => item.id.toLowerCase() === "instagram" && item.isEnable && item.link?.trim()
  );
  return instagramItem?.link || "";
};

/**
 * Extract specific social media link by platform name
 * @param socialMediaList - Array of social media items
 * @param platform - Platform name (e.g., "instagram", "x", "facebook")
 * @returns Social media link if exists and is not hidden, empty string otherwise
 */
export const getSocialMediaLink = (
  socialMediaList: EachSocialMediaItem[],
  platform: string
): string => {
  const socialMediaItem = socialMediaList?.find(
    (item) => item.id.toLowerCase() === platform.toLowerCase() && item.isEnable && item.link?.trim()
  );
  return socialMediaItem?.link || "";
};

/**
 * Get all visible social media links
 * @param socialMediaList - Array of social media items
 * @returns Array of visible social media items with links
 */
export const getVisibleSocialMediaLinks = (
  socialMediaList: EachSocialMediaItem[]
): EachSocialMediaItem[] => {
  return socialMediaList?.filter(
    (item) => item.isEnable && item.link?.trim()
  ) || [];
};
