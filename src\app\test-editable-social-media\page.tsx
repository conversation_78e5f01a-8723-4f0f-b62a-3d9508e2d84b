'use client'
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { fetchCoachSocialMediaLinks } from '@/store/slices/coach/coachProfileSlice';
import EditableSocialMedia from '@/components/common/EditableSocialMedia';
import { handleCoachInputChange } from '@/store/slices/coach/coachProfileSlice';

const TestEditableSocialMediaPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { coachSocialMediaList, toggleSocialMedia, loading } = useSelector((state: RootState) => state.coachProfile);

  useEffect(() => {
    dispatch(fetchCoachSocialMediaLinks());
  }, [dispatch]);

  const handleToggleSection = (checked: boolean) => {
    dispatch(handleCoachInputChange({
      name: 'toggleSocialMedia',
      value: checked
    }));
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Editable Social Media Test Page</h1>
      
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Instructions</h2>
        <ul className="list-disc list-inside space-y-2 text-sm">
          <li>Click the pencil icon next to any social media platform to edit</li>
          <li>Update the URL and toggle visibility (Hidden/Visible)</li>
          <li>Click the green checkmark to save changes</li>
          <li>Click the X to cancel editing</li>
          <li>Changes are saved individually for each platform</li>
        </ul>
      </div>

      <div className="mb-8 p-4 bg-yellow-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">API Endpoint</h2>
        <p className="text-sm font-mono bg-white p-2 rounded">
          PUT /coach-social-media/{`{userId}`}
        </p>
        <p className="text-sm mt-2">
          <strong>Payload:</strong> {`{ socialMedia: string, socialMediaLink: string, isHidden: boolean }`}
        </p>
      </div>

      {/* Editable Social Media Component */}
      <div className="mb-8">
        <EditableSocialMedia
          origin="coach"
          list={coachSocialMediaList}
          toggleSocialMediaSection={toggleSocialMedia}
          onChangeToggleSection={handleToggleSection}
          loading={loading}
          fetchLoading={loading}
        />
      </div>

      {/* Current State Display */}
      <div className="mb-8 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Current Social Media State</h2>
        <div className="space-y-2">
          {coachSocialMediaList?.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-2 bg-white rounded border">
              <div className="flex items-center gap-3">
                <img src={item.icon} alt={item.id} className="h-6 w-6" />
                <span className="font-medium">{item.id}</span>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <span className="truncate max-w-xs" title={item.link}>
                  {item.link || 'No link'}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  item.isEnable 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {item.isEnable ? 'Visible' : 'Hidden'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Raw Data for Debugging */}
      <details className="mb-8">
        <summary className="cursor-pointer font-semibold text-lg mb-4">
          View Raw Data (Debug)
        </summary>
        <div className="p-4 bg-gray-100 rounded-lg">
          <pre className="text-sm overflow-auto">
            {JSON.stringify(coachSocialMediaList, null, 2)}
          </pre>
        </div>
      </details>

      {/* Expected API Response Format */}
      <div className="mb-8 p-4 bg-green-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Expected API Response Format</h2>
        <pre className="text-sm overflow-auto bg-white p-3 rounded">
{`// GET /coach-social-media?userId={userId}
[
  {
    "id": 7,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "Instagram",
    "socialMediaLink": "https://instagram.com/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  }
]

// PUT /coach-social-media/{userId}
// Request Body:
{
  "socialMedia": "Instagram",
  "socialMediaLink": "https://instagram.com/new_profile",
  "isHidden": false
}`}
        </pre>
      </div>
    </div>
  );
};

export default TestEditableSocialMediaPage;
