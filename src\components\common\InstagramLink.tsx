'use client'
import { EachSocialMediaItem } from "@/utils/interfaces";
import { getInstagramLink } from "@/utils/socialMediaHelpers";
import { Instagram } from "lucide-react";

interface InstagramLinkProps {
  socialMediaList: EachSocialMediaItem[];
  className?: string;
  showIcon?: boolean;
  showLabel?: boolean;
}

const InstagramLink = ({ 
  socialMediaList, 
  className = "", 
  showIcon = true, 
  showLabel = false 
}: InstagramLinkProps) => {
  const instagramLink = getInstagramLink(socialMediaList);

  if (!instagramLink) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showIcon && (
        <Instagram className="w-5 h-5 text-pink-500" />
      )}
      {showLabel && (
        <span className="text-sm font-medium">Instagram:</span>
      )}
      <a
        href={instagramLink}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 hover:underline truncate"
        title={instagramLink}
      >
        {instagramLink}
      </a>
    </div>
  );
};

export default InstagramLink;
