'use client'
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch } from "@/store";
import { editCoachSocialMediaLinks, fetchCoachSocialMediaLinks } from "@/store/slices/coach/coachProfileSlice";
import { EachSocialMediaItem } from "@/utils/interfaces";
import { Check, Loader, PencilLine, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { Skeleton } from "../ui/skeleton";
import UpgradePremiumSection from "./UpgradePremiumSection";

interface IProps {
    toggleSocialMediaSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
    list: EachSocialMediaItem[];
    origin: string;
    loading?: boolean;
    fetchLoading?: boolean;
}

const EditableSocialMedia = ({
    toggleSocialMediaSection,
    onChangeToggleSection,
    list,
    origin,
    loading,
    fetchLoading,
}: IProps) => {
    const [socialMediaList, setSocialMediaList] = useState<EachSocialMediaItem[]>(list)
    const [editableIndex, setEditableIndex] = useState<number | null>(null);
    const [editingItem, setEditingItem] = useState<{link: string, isHidden: boolean} | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);
    const dispatch = useDispatch<AppDispatch>()
    const { userId, roleId, isPremiumUser } = useTokenValues()
    const isAthletePremiumUser = origin === 'athlete' && isPremiumUser
    const canEdit = origin === 'coach' || isAthletePremiumUser

    useEffect(() => {
        list && setSocialMediaList(list)
    }, [list])

    const onClickEdit = (index: number) => {
        const item = socialMediaList[index];
        setEditableIndex(index);
        setEditingItem({
            link: item.link,
            isHidden: !item.isEnable
        });
    };

    const onClickCancel = () => {
        setEditableIndex(null);
        setEditingItem(null);
    };

    const onChangeLinkValue = (value: string) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                link: value
            });
        }
    };

    const onToggleVisibility = (checked: boolean) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                isHidden: !checked
            });
        }
    };

    const handleSaveIndividualLink = async () => {
        if (editableIndex === null || !editingItem) return;

        const item = socialMediaList[editableIndex];
        setIsUpdating(true);

        try {
            const payload = {
                socialMedia: item.id,
                socialMediaLink: editingItem.link,
                isHidden: editingItem.isHidden
            };

            const resultAction = await dispatch(editCoachSocialMediaLinks(payload));
            
            if (editCoachSocialMediaLinks.fulfilled.match(resultAction)) {
                // Update local state
                const updatedList = [...socialMediaList];
                updatedList[editableIndex] = {
                    ...item,
                    link: editingItem.link,
                    isEnable: !editingItem.isHidden
                };
                setSocialMediaList(updatedList);
                
                // Reset editing state
                setEditableIndex(null);
                setEditingItem(null);
                
                // Refresh data from server
                await dispatch(fetchCoachSocialMediaLinks());
                
                toast.success(`${item.id} link updated successfully!`);
            } else {
                toast.error('Failed to update social media link');
            }
        } catch (error) {
            console.error('Error updating social media link:', error);
            toast.error('An error occurred while updating');
        } finally {
            setIsUpdating(false);
        }
    };

    if (fetchLoading) {
        return (
            <div className="flex flex-col items-center gap-6">
                <Skeleton className="h-[20px] w-full rounded-lg " />
                <Skeleton className="h-[20px] w-full rounded-lg " />
                <Skeleton className="h-[20px] w-full rounded-lg " />
            </div>
        )
    }

    return (
        <div className="w-full flex flex-col gap-8 bg-slate-100 p-4 rounded-lg">
            {!canEdit && <UpgradePremiumSection />}
            <div className="flex items-center justify-center gap-4">
                <h3 className="font-bold text-xl text-center">Social Media</h3>
                <Switch
                    name="toggleSocialMediaSection"
                    checked={toggleSocialMediaSection}
                    onCheckedChange={onChangeToggleSection}
                    disabled={!canEdit}
                />
            </div>

            {toggleSocialMediaSection && (
                <div className="flex flex-col justify-between gap-4 w-full">
                    {socialMediaList?.map((item, index) => (
                        <div key={item?.id} className="w-full flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
                            {/* Icon */}
                            <img src={item?.icon} alt={item?.id} className="h-8 w-8 shrink-0" loading="lazy" />

                            {/* Link/Input Section */}
                            <div className="flex-1 flex flex-col gap-2 min-w-0">
                                {editableIndex === index ? (
                                    <>
                                        {/* Edit Mode */}
                                        <div className="flex items-center gap-2">
                                            <Input
                                                value={editingItem?.link || ''}
                                                onChange={(e) => onChangeLinkValue(e.target.value)}
                                                className="flex-1 border-slate-500"
                                                placeholder={`Enter ${item?.id} URL`}
                                                disabled={!canEdit || isUpdating}
                                            />
                                        </div>
                                        
                                        {/* Visibility Toggle */}
                                        <div className="flex items-center gap-2 text-sm">
                                            <span>Visible:</span>
                                            <Switch
                                                checked={!editingItem?.isHidden}
                                                onCheckedChange={onToggleVisibility}
                                                disabled={!canEdit || isUpdating}
                                            />
                                            <span className="text-gray-500">
                                                {editingItem?.isHidden ? 'Hidden' : 'Visible'}
                                            </span>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex items-center gap-2">
                                            <Button
                                                size="sm"
                                                onClick={handleSaveIndividualLink}
                                                disabled={!canEdit || isUpdating}
                                                className="bg-green-600 hover:bg-green-700"
                                            >
                                                {isUpdating ? (
                                                    <Loader className="w-4 h-4 animate-spin" />
                                                ) : (
                                                    <Check className="w-4 h-4" />
                                                )}
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={onClickCancel}
                                                disabled={isUpdating}
                                            >
                                                <X className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        {/* Display Mode */}
                                        <div className="flex items-center gap-2">
                                            <div className="flex-1 min-w-0">
                                                {item?.link ? (
                                                    <a
                                                        href={item?.link}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="block text-blue-900 hover:underline truncate"
                                                        title={item?.link}
                                                    >
                                                        {item?.link}
                                                    </a>
                                                ) : (
                                                    <span className="text-gray-400 italic">No link added</span>
                                                )}
                                            </div>
                                            
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onClickEdit(index)}
                                                disabled={!canEdit}
                                            >
                                                <PencilLine className="w-4 h-4" />
                                            </Button>
                                        </div>

                                        {/* Status Display */}
                                        <div className="flex items-center gap-2 text-sm">
                                            <span className={`px-2 py-1 rounded-full text-xs ${
                                                item.isEnable 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-gray-100 text-gray-600'
                                            }`}>
                                                {item.isEnable ? 'Visible' : 'Hidden'}
                                            </span>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default EditableSocialMedia;
