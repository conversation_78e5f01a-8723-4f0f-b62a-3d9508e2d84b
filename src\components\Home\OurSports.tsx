"use client";
import React, { useEffect, useRef } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
} from "@/utils/parseOptions";

const OurSports = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });
  const autoplayRef = useRef<NodeJS.Timeout | null>(null);

  const { ourSportsSection, ourSportsImages, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  const startAutoplay = () => {
    if (autoplayRef.current) clearInterval(autoplayRef.current);
    autoplayRef.current = setInterval(() => {
      if (emblaApi) emblaApi.scrollNext();
    }, 2000);
  };

  const stopAutoplay = () => {
    if (autoplayRef.current) clearInterval(autoplayRef.current);
  };

  useEffect(() => {
    if (emblaApi) startAutoplay();
    return () => stopAutoplay();
  }, [emblaApi]);

  if (loading || !ourSportsSection) return null;

  return (
    <section
      id="our-sports"
      className="py-12 bg-[#0D1D3A] px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56"
    >
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-4">
            {parse(ourSportsSection.title || "", titleParseOptions)}
          </h2>
          <p className="cms-text max-w-3xl mx-auto text-gray-300">
            {parse(
              ourSportsSection.description || "",
              shortDescriptionParseOptions
            )}
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex -ml-2 md:-ml-4">
              {ourSportsImages.map((img) => (
                <div
                  key={img.id}
                  className="flex-[0_0_100%] md:flex-[0_0_50%] lg:flex-[0_0_33.333%] pl-3 md:pl-6"
                  onMouseEnter={stopAutoplay}
                  onMouseLeave={startAutoplay}
                >
                  {/* remove vh-based heights; use a consistent, responsive fixed height */}
                  <div className="group cursor-pointer">
                    <div className="bg-[#13294B] rounded-2xl p-4 shadow-md border border-white/10 transition hover:-translate-y-1 flex flex-col">
                      {/* Image with aspect ratio so height scales predictably */}
                      {img.fileLocation && (
                        <div className="w-full aspect-[16/9] overflow-hidden rounded-xl">
                          <img
                            src={img.fileLocation}
                            alt="sport"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      {/* Title */}
                      <h3 className="mt-3 text-lg font-semibold text-white ...">
                        {parse(img.fileTitle || "", fileTitleParseOptions)}
                      </h3>
                      {/* If you add a description later, put it here and let it wrap naturally */}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurSports;
