'use client'
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { getInstagramLink } from '@/utils/socialMediaHelpers';
import InstagramLink from '@/components/common/InstagramLink';

/**
 * Example component showing different ways to display Instagram link
 */
const InstagramLinkExample = () => {
  const { coachSocialMediaList } = useSelector((state: RootState) => state.coachProfile);

  // Method 1: Using the helper function directly
  const instagramLink = getInstagramLink(coachSocialMediaList);

  return (
    <div className="space-y-6 p-4">
      <h2 className="text-2xl font-bold">Instagram Link Display Examples</h2>

      {/* Example 1: Simple text link */}
      {instagramLink && (
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">Simple Text Link</h3>
          <a 
            href={instagramLink} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {instagramLink}
          </a>
        </div>
      )}

      {/* Example 2: Using InstagramLink component with icon */}
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">With Icon</h3>
        <InstagramLink 
          socialMediaList={coachSocialMediaList}
          showIcon={true}
          showLabel={false}
        />
      </div>

      {/* Example 3: Using InstagramLink component with label */}
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">With Label and Icon</h3>
        <InstagramLink 
          socialMediaList={coachSocialMediaList}
          showIcon={true}
          showLabel={true}
        />
      </div>

      {/* Example 4: Custom styled */}
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">Custom Styled</h3>
        <InstagramLink 
          socialMediaList={coachSocialMediaList}
          showIcon={true}
          showLabel={true}
          className="bg-gradient-to-r from-purple-400 to-pink-400 text-white p-3 rounded-lg"
        />
      </div>

      {/* Example 5: Conditional display */}
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">Conditional Display</h3>
        {instagramLink ? (
          <div className="flex items-center gap-2 text-green-600">
            <span>✓ Instagram profile available:</span>
            <InstagramLink 
              socialMediaList={coachSocialMediaList}
              showIcon={false}
              showLabel={false}
            />
          </div>
        ) : (
          <p className="text-gray-500">No Instagram profile available</p>
        )}
      </div>

      {/* Debug info */}
      <div className="p-4 border rounded-lg bg-gray-50">
        <h3 className="font-semibold mb-2">Debug Information</h3>
        <p><strong>Instagram Link Found:</strong> {instagramLink || 'None'}</p>
        <p><strong>Total Social Media Items:</strong> {coachSocialMediaList?.length || 0}</p>
        <details className="mt-2">
          <summary className="cursor-pointer font-medium">View Raw Data</summary>
          <pre className="mt-2 text-xs overflow-auto bg-white p-2 rounded">
            {JSON.stringify(coachSocialMediaList, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default InstagramLinkExample;
