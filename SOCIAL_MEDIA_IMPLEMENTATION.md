# Social Media Implementation Guide

## Overview
This implementation provides comprehensive functionality to display and edit coach social media links, specifically Instagram links, from the API response. The system fetches social media data from the API, displays it in various components, and allows individual editing of each social media platform with visibility control.

## API Integration

### API Endpoints

#### Fetch Social Media Links
```
GET ${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media?userId=${userId}
```

#### Edit Individual Social Media Link
```
PUT ${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media/${userId}
```

**Request Body:**
```json
{
  "socialMedia": "Instagram",
  "socialMediaLink": "https://instagram.com/new_profile",
  "isHidden": false
}
```

### Expected API Response Format
```json
[
  {
    "id": 7,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "Instagram",
    "socialMediaLink": "https://instagram.com/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  },
  {
    "id": 8,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "X",
    "socialMediaLink": "https://X.com/in/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  }
]
```

## Redux Store Integration

### Slice Update
The `fetchCoachSocialMediaLinks.fulfilled` case in `coachProfileSlice.ts` has been updated to properly merge API data with the existing social media list:

```typescript
.addCase(fetchCoachSocialMediaLinks.fulfilled, (state, action) => {
  state.loading = false;
  
  // Create a map of API data for easy lookup
  const apiDataMap = new Map();
  action.payload?.forEach((item: any) => {
    apiDataMap.set(item.socialMedia.toLowerCase(), {
      link: item.socialMediaLink,
      isHidden: item.isHidden,
    });
  });

  // Update the existing coachSocialMediaList with API data
  state.coachSocialMediaList = state.coachSocialMediaList.map((item) => {
    const apiData = apiDataMap.get(item.id.toLowerCase());
    return {
      ...item,
      link: apiData?.link || item.link,
      isEnable: apiData ? !apiData.isHidden : item.isEnable,
    };
  });
})
```

## Utility Functions

### Social Media Helpers (`src/utils/socialMediaHelpers.ts`)

#### `getInstagramLink(socialMediaList: EachSocialMediaItem[]): string`
Extracts the Instagram link from the social media list if it exists and is not hidden.

#### `getSocialMediaLink(socialMediaList: EachSocialMediaItem[], platform: string): string`
Extracts a specific social media link by platform name.

#### `getVisibleSocialMediaLinks(socialMediaList: EachSocialMediaItem[]): EachSocialMediaItem[]`
Returns all visible social media links that have values.

## Components

### InstagramLink Component (`src/components/common/InstagramLink.tsx`)
A reusable component to display Instagram links with customizable options.

**Props:**
- `socialMediaList: EachSocialMediaItem[]` - Array of social media items
- `className?: string` - Additional CSS classes
- `showIcon?: boolean` - Whether to show Instagram icon (default: true)
- `showLabel?: boolean` - Whether to show "Instagram:" label (default: false)

**Usage:**
```tsx
import InstagramLink from '@/components/common/InstagramLink';

<InstagramLink
  socialMediaList={coachSocialMediaList}
  showIcon={true}
  showLabel={false}
  className="justify-center text-sm"
/>
```

### EditableSocialMedia Component (`src/components/common/EditableSocialMedia.tsx`)
**NEW**: An enhanced social media component that allows individual editing of each platform with visibility control.

**Features:**
- Individual edit mode for each social media platform
- Real-time visibility toggle (Hidden/Visible)
- Individual save/cancel actions
- Loading states during API calls
- Automatic data refresh after successful updates

**Props:**
- `toggleSocialMediaSection: boolean` - Controls section visibility
- `onChangeToggleSection: (checked: boolean) => void` - Toggle section callback
- `list: EachSocialMediaItem[]` - Array of social media items
- `origin: string` - Component origin ("coach", "athlete", etc.)
- `loading?: boolean` - Loading state
- `fetchLoading?: boolean` - Fetch loading state

**Usage:**
```tsx
import EditableSocialMedia from '@/components/common/EditableSocialMedia';

<EditableSocialMedia
  origin="coach"
  list={coachSocialMediaList}
  toggleSocialMediaSection={toggleSocialMedia}
  onChangeToggleSection={(checked) => handleToggleChange('toggleSocialMedia', checked)}
/>
```

### CoachSocialMediaDisplay Component (`src/components/coach/coachProfile/CoachSocialMediaDisplay.tsx`)
A comprehensive component that displays all social media links with special highlighting for Instagram.

## Integration Examples

### In CoachAboutCard Component
The Instagram link has been integrated into the coach profile card:

```tsx
{/* Instagram Link Display */}
<div className="mt-2">
  <InstagramLink 
    socialMediaList={coachSocialMediaList} 
    showIcon={true}
    showLabel={false}
    className="justify-center text-sm"
  />
</div>
```

### Using Helper Functions
```tsx
import { getInstagramLink, getSocialMediaLink } from '@/utils/socialMediaHelpers';

const MyComponent = () => {
  const { coachSocialMediaList } = useSelector((state: RootState) => state.coachProfile);
  
  const instagramLink = getInstagramLink(coachSocialMediaList);
  const xLink = getSocialMediaLink(coachSocialMediaList, 'x');
  
  return (
    <div>
      {instagramLink && (
        <a href={instagramLink} target="_blank" rel="noopener noreferrer">
          Follow on Instagram
        </a>
      )}
    </div>
  );
};
```

## Testing

### Test Page
A test page has been created at `/test-social-media` to demonstrate all functionality:
- Raw API data display
- Instagram link component
- Helper function results
- Full social media display component

### How to Test
1. Run the application: `npm run dev`
2. Navigate to `http://localhost:3001/test-social-media`
3. Check the coach profile page to see the Instagram link in the about card

## Key Features

1. **Automatic Data Merging**: API data is automatically merged with the existing social media structure
2. **Individual Editing**: Each social media platform can be edited independently
3. **Visibility Control**: Links can be toggled between visible and hidden states
4. **Real-time Updates**: Changes are saved immediately with API calls
5. **Reusable Components**: Components can be used anywhere in the application
6. **Type Safety**: Full TypeScript support with proper interfaces
7. **Flexible Display**: Customizable icon and label display options
8. **Loading States**: Proper loading indicators during API operations

## Individual Editing Workflow

1. **Edit Mode**: Click the pencil icon next to any social media platform
2. **Update Link**: Modify the URL in the input field
3. **Toggle Visibility**: Use the switch to control if the link is visible or hidden
4. **Save Changes**: Click the green checkmark to save (calls `editCoachSocialMediaLinks` API)
5. **Cancel Changes**: Click the X to cancel without saving
6. **Auto Refresh**: Data is automatically refreshed from the server after successful updates

## Redux Actions

### `editCoachSocialMediaLinks`
```typescript
// Payload type
{
  socialMedia: string;      // Platform name (e.g., "Instagram")
  socialMediaLink: string;  // URL
  isHidden: boolean;        // Visibility flag
}

// Usage
dispatch(editCoachSocialMediaLinks({
  socialMedia: "Instagram",
  socialMediaLink: "https://instagram.com/new_profile",
  isHidden: false
}));
```

## Testing Pages

1. **Basic Display**: `/test-social-media` - Shows Instagram link extraction and display
2. **Editable Interface**: `/test-editable-social-media` - Full editing functionality demo

## Notes

- The Instagram link will only display if it exists in the API response and `isHidden` is false
- The system is case-insensitive when matching social media platforms
- All components handle empty or missing data gracefully
- Individual edits are saved immediately to the server
- The implementation follows the existing codebase patterns and conventions
- The `EditableSocialMedia` component has replaced the original `SocialMedia` component in the coach profile
